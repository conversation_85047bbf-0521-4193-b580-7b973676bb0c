<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.microcredchina.xwikiwatermark</groupId>
    <artifactId>xwiki-watermark-extension</artifactId>
    <version>1.1</version>
    <packaging>xar</packaging>

    <name>XWiki Watermark Extension</name>
    <description>Professional watermark extension for XWiki with Canvas rendering, placeholder support, anti-copy protection and mobile compatibility</description>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <xwiki.version>17.4.3</xwiki.version>
    </properties>

    <dependencies>
        <!-- Minimal XWiki dependencies for XAR packaging -->
        <dependency>
            <groupId>org.xwiki.commons</groupId>
            <artifactId>xwiki-commons-component-api</artifactId>
            <version>${xwiki.version}</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>

    <build>
        <extensions>
            <!-- XAR packaging extension -->
            <extension>
                <groupId>org.xwiki.commons</groupId>
                <artifactId>xwiki-commons-tool-xar-handlers</artifactId>
                <version>${xwiki.version}</version>
            </extension>
        </extensions>

        <plugins>
            <!-- XAR Plugin for building XWiki extensions -->
            <plugin>
                <groupId>org.xwiki.commons</groupId>
                <artifactId>xwiki-commons-tool-xar-plugin</artifactId>
                <version>${xwiki.version}</version>
            </plugin>
        </plugins>
    </build>


    <repositories>
        <repository>
            <id>xwiki-releases</id>
            <url>https://nexus.xwiki.org/nexus/content/groups/public</url>
        </repository>
    </repositories>
    <pluginRepositories>
        <pluginRepository>
            <id>xwiki-plugins</id>
            <url>https://nexus.xwiki.org/nexus/content/groups/public</url>
        </pluginRepository>
    </pluginRepositories>


</project>